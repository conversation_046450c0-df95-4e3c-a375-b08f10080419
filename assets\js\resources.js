// Resources page functionality

document.addEventListener('DOMContentLoaded', function() {
    initializeResourceFilters();
    initializeAccessibilityFeatures();
});

// Resource filtering functionality
function initializeResourceFilters() {
    const filterButtons = document.querySelectorAll('[data-filter]');
    const resourceItems = document.querySelectorAll('.resource-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter resources
            filterResources(filter, resourceItems);
            
            // Announce filter change to screen readers
            const activeCount = document.querySelectorAll('.resource-item:not(.d-none)').length;
            announceToScreenReader(`Showing ${activeCount} resources for ${filter === 'all' ? 'all categories' : filter + ' category'}`);
        });
    });
}

function filterResources(filter, items) {
    items.forEach(item => {
        const category = item.getAttribute('data-category');
        
        if (filter === 'all' || category === filter) {
            item.classList.remove('d-none');
            // Add fade-in animation
            item.style.opacity = '0';
            setTimeout(() => {
                item.style.opacity = '1';
            }, 100);
        } else {
            item.classList.add('d-none');
        }
    });
}

// Accessibility enhancements for resources page
function initializeAccessibilityFeatures() {
    // Add keyboard navigation for resource cards
    const resourceCards = document.querySelectorAll('.resource-item .card');
    resourceCards.forEach(card => {
        card.setAttribute('tabindex', '0');
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const link = card.querySelector('a');
                if (link) {
                    link.click();
                }
            }
        });
    });

    // Add focus management for filter buttons
    const filterButtons = document.querySelectorAll('[data-filter]');
    filterButtons.forEach((button, index) => {
        button.addEventListener('keydown', function(e) {
            let targetIndex;
            
            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    targetIndex = index > 0 ? index - 1 : filterButtons.length - 1;
                    filterButtons[targetIndex].focus();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    targetIndex = index < filterButtons.length - 1 ? index + 1 : 0;
                    filterButtons[targetIndex].focus();
                    break;
                case 'Home':
                    e.preventDefault();
                    filterButtons[0].focus();
                    break;
                case 'End':
                    e.preventDefault();
                    filterButtons[filterButtons.length - 1].focus();
                    break;
            }
        });
    });

    // Add download tracking
    const downloadLinks = document.querySelectorAll('a[download]');
    downloadLinks.forEach(link => {
        link.addEventListener('click', function() {
            const resourceTitle = this.closest('.card').querySelector('.card-title').textContent.trim();
            announceToScreenReader(`Starting download of ${resourceTitle}`);
            
            // Track download (you can send this to analytics)
            console.log('Resource downloaded:', resourceTitle);
        });
    });

    // Add external link warnings
    const externalLinks = document.querySelectorAll('a[target="_blank"]');
    externalLinks.forEach(link => {
        link.addEventListener('click', function() {
            const resourceTitle = this.closest('.card').querySelector('.card-title').textContent.trim();
            announceToScreenReader(`Opening ${resourceTitle} in a new window`);
        });
    });
}

// Translation service request
function requestTranslation() {
    const languages = [
        'Spanish', 'French', 'German', 'Italian', 'Portuguese', 
        'Chinese (Mandarin)', 'Japanese', 'Korean', 'Arabic', 'Russian'
    ];
    
    let languageOptions = languages.map(lang => `<option value="${lang}">${lang}</option>`).join('');
    
    const modalHTML = `
        <div class="modal fade" id="translationModal" tabindex="-1" aria-labelledby="translationModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="translationModalLabel">Request Translation Service</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="translationForm">
                            <div class="mb-3">
                                <label for="translationLanguage" class="form-label">Preferred Language</label>
                                <select class="form-select" id="translationLanguage" required>
                                    <option value="">Select a language</option>
                                    ${languageOptions}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="translationContact" class="form-label">Contact Information</label>
                                <input type="text" class="form-control" id="translationContact" 
                                       placeholder="Phone number or email" required>
                            </div>
                            <div class="mb-3">
                                <label for="translationUrgency" class="form-label">Urgency Level</label>
                                <select class="form-select" id="translationUrgency" required>
                                    <option value="">Select urgency</option>
                                    <option value="immediate">Immediate (within 1 hour)</option>
                                    <option value="urgent">Urgent (within 4 hours)</option>
                                    <option value="standard">Standard (within 24 hours)</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="translationNotes" class="form-label">Additional Notes</label>
                                <textarea class="form-control" id="translationNotes" rows="3" 
                                          placeholder="Describe what you need help with"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="submitTranslationRequest()">
                            <i class="fas fa-paper-plane me-1"></i>Submit Request
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to page if it doesn't exist
    if (!document.getElementById('translationModal')) {
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('translationModal'));
    modal.show();
    
    // Focus on first form field when modal opens
    document.getElementById('translationModal').addEventListener('shown.bs.modal', function() {
        document.getElementById('translationLanguage').focus();
    });
}

function submitTranslationRequest() {
    const form = document.getElementById('translationForm');
    const language = document.getElementById('translationLanguage').value;
    const contact = document.getElementById('translationContact').value;
    const urgency = document.getElementById('translationUrgency').value;
    const notes = document.getElementById('translationNotes').value;
    
    // Basic validation
    if (!language || !contact || !urgency) {
        showAlert('danger', 'Please fill in all required fields.');
        return;
    }
    
    // Simulate form submission
    const submitButton = document.querySelector('#translationModal .btn-primary');
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Submitting...';
    submitButton.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('translationModal'));
        modal.hide();
        
        // Show success message
        showAlert('success', `Translation request submitted successfully! We will contact you at ${contact} for ${urgency} service in ${language}.`);
        
        // Reset form
        form.reset();
        
        // Restore button
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
        
        // Announce to screen reader
        announceToScreenReader('Translation request submitted successfully. You will be contacted soon.');
        
    }, 2000);
}

// Utility functions (if not already defined in main.js)
function showAlert(type, message) {
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertContainer.style.cssText = 'top: 100px; right: 20px; z-index: 1050; max-width: 400px;';
    alertContainer.setAttribute('role', 'alert');
    alertContainer.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(alertContainer);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertContainer.parentNode) {
            alertContainer.remove();
        }
    }, 5000);
}

function announceToScreenReader(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'visually-hidden';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
        if (document.body.contains(announcement)) {
            document.body.removeChild(announcement);
        }
    }, 1000);
}
