/* General Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding-top: 76px;
}

/* Hero Section */
.hero-section {
    background-color: var(--light-color);
    padding: 100px 0;
}

.hero-section h1 {
    color: var(--dark-color);
    margin-bottom: 1.5rem;
}

.hero-section img {
    max-width: 100%;
    height: auto;
}

/* Services Section */
.card {
    border: none;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-5px);
}

.card-body i {
    color: var(--primary-color);
}

/* Footer */
footer {
    background-color: var(--dark-color);
}

footer a {
    color: var(--light-color);
    text-decoration: none;
}

footer a:hover {
    color: var(--primary-color);
}

.social-links a {
    display: inline-block;
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 10px;
}

.social-links a:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 60px 0;
    }
    
    .hero-section img {
        margin-top: 2rem;
    }
}
