/* General Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --dark-color: #212529;
    --light-color: #f8f9fa;
    --focus-color: #0a58ca;
    --error-color: #dc3545;
    --success-color: #198754;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding-top: 76px;
    line-height: 1.6;
}

/* Accessibility Styles */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--dark-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    z-index: 1000;
    font-weight: bold;
}

.skip-link:focus {
    top: 0;
}

/* Focus indicators for better keyboard navigation */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus,
.btn:focus {
    outline: 3px solid var(--focus-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0056b3;
        --dark-color: #000000;
        --light-color: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Hero Section */
.hero-section {
    background-color: var(--light-color);
    padding: 100px 0;
}

.hero-section h1 {
    color: var(--dark-color);
    margin-bottom: 1.5rem;
}

.hero-section img {
    max-width: 100%;
    height: auto;
}

/* Services Section */
.card {
    border: none;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-5px);
}

.card-body i {
    color: var(--primary-color);
}

/* Footer */
footer {
    background-color: var(--dark-color);
}

footer a {
    color: var(--light-color);
    text-decoration: none;
}

footer a:hover {
    color: var(--primary-color);
}

.social-links a {
    display: inline-block;
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 10px;
}

.social-links a:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Contact Section */
.contact-info h3 {
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.contact-method h4 {
    font-size: 1.1rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.contact-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.contact-link:hover,
.contact-link:focus {
    color: var(--focus-color);
    text-decoration: underline;
}

/* Form Styles */
.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

.required {
    color: var(--error-color);
    font-weight: bold;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--focus-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-text {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.invalid-feedback {
    display: block;
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: var(--error-color);
}

.alert {
    border-radius: 8px;
    margin-bottom: 1rem;
}

/* Enhanced Visual Design */
.hero-section img {
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
}

.hero-section img:hover {
    transform: translateY(-5px);
}

/* Image placeholder styling */
.img-placeholder {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    min-height: 300px;
    border-radius: 12px;
}

/* Enhanced card styling */
.card {
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.card-body {
    padding: 2rem;
}

/* Button enhancements */
.btn {
    border-radius: 8px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-primary:hover,
.btn-primary:focus {
    background: linear-gradient(135deg, #0056b3, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Typography enhancements */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
}

.display-4 {
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Section spacing */
section {
    position: relative;
}

.hero-banner {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
}

/* Loading animations */
.loaded {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* High contrast improvements */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3) !important;
}

/* Resource page specific styles */
.resource-item {
    transition: all 0.3s ease;
}

.resource-item.d-none {
    display: none !important;
}

.accessibility-features {
    border-left: 3px solid var(--success-color);
    padding-left: 0.75rem;
    margin-left: 0.5rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* Alert enhancements */
.alert {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.alert-success {
    background: linear-gradient(135deg, #198754, #157347);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #0dcaf0, #0aa2c0);
    color: white;
}

/* Character counter */
.character-counter {
    text-align: right;
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 60px 0;
    }

    .hero-section img {
        margin-top: 2rem;
    }

    .contact-form {
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .contact-method {
        text-align: center;
        margin-bottom: 2rem;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 8px !important;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 40px 0;
    }

    .display-4 {
        font-size: 2rem;
    }

    .lead {
        font-size: 1rem;
    }
}
