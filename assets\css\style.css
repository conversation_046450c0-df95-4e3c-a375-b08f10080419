/* NCare-inspired Theme Colors */
:root {
    --primary-color: #1CBBB4;
    --primary-dark: #00A693;
    --secondary-color: #FF6B35;
    --accent-color: #4ECDC4;
    --dark-color: #2C3E50;
    --light-color: #F8FFFE;
    --text-color: #34495E;
    --text-light: #7F8C8D;
    --focus-color: #16A085;
    --error-color: #E74C3C;
    --success-color: #27AE60;
    --warning-color: #F39C12;
    --white: #FFFFFF;
    --section-bg: #F7FDFC;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    padding-top: 80px;
    line-height: 1.7;
    color: var(--text-color);
    background-color: var(--white);
    font-size: 16px;
    font-weight: 400;
}

/* Accessibility Styles */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--dark-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    z-index: 1000;
    font-weight: bold;
}

.skip-link:focus {
    top: 0;
}

/* Focus indicators for better keyboard navigation */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus,
.btn:focus {
    outline: 3px solid var(--focus-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0056b3;
        --dark-color: #000000;
        --light-color: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Hero Section - NCare Style */
.hero-section {
    background: linear-gradient(135deg, var(--section-bg) 0%, var(--white) 100%);
    padding: 120px 0 100px;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: linear-gradient(135deg, rgba(28, 187, 180, 0.05) 0%, rgba(76, 205, 196, 0.1) 100%);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    color: var(--dark-color);
    margin-bottom: 2rem;
    font-weight: 700;
    line-height: 1.2;
}

.hero-section .lead {
    color: var(--text-light);
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

.hero-section img {
    max-width: 100%;
    height: auto;
}

/* Services Section - NCare Style */
.card {
    border: none;
    transition: all 0.4s ease;
    box-shadow: 0 8px 30px rgba(28, 187, 180, 0.1);
    border-radius: 20px;
    background: var(--white);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(28, 187, 180, 0.2);
}

.card-body {
    padding: 2.5rem 2rem;
    text-align: center;
}

.card-body i {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 3.5rem;
    transition: all 0.3s ease;
}

.card:hover .card-body i {
    color: var(--secondary-color);
    transform: scale(1.1);
}

.card-title {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.card-text {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

/* Navigation - NCare Style */
.navbar {
    background: var(--white) !important;
    box-shadow: 0 2px 20px rgba(28, 187, 180, 0.1);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

/* Ensure navbar is always white background */
.navbar-light {
    background-color: var(--white) !important;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.8rem;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    color: var(--text-color) !important;
    font-weight: 500;
    padding: 0.75rem 1.25rem !important;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
}

/* Footer - NCare Style */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, #34495E 100%);
    color: var(--white);
    padding: 3rem 0 1rem;
}

footer h5 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
}

footer h5::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--primary-color);
}

footer a {
    color: #BDC3C7;
    text-decoration: none;
    transition: all 0.3s ease;
}

footer a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

footer ul li {
    margin-bottom: 0.5rem;
}

.social-links a {
    display: inline-block;
    width: 45px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    background: rgba(28, 187, 180, 0.1);
    border-radius: 50%;
    margin-right: 15px;
    color: var(--primary-color);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.social-links a:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-3px);
    border-color: var(--primary-color);
}

/* Contact Section */
.contact-info h3 {
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.contact-method h4 {
    font-size: 1.1rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.contact-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.contact-link:hover,
.contact-link:focus {
    color: var(--focus-color);
    text-decoration: underline;
}

/* Form Styles */
.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

.required {
    color: var(--error-color);
    font-weight: bold;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--focus-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-text {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.invalid-feedback {
    display: block;
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: var(--error-color);
}

.alert {
    border-radius: 8px;
    margin-bottom: 1rem;
}

/* Hero Banner - NCare Style */
.hero-banner {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 2.5rem 0;
    position: relative;
    overflow: hidden;
}

.hero-banner::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 100%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-20px) rotate(360deg); }
}

.hero-banner h1,
.hero-banner h2 {
    color: var(--white);
    position: relative;
    z-index: 2;
}

/* Enhanced Visual Design */
.hero-section img {
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(28, 187, 180, 0.2);
    transition: all 0.4s ease;
}

.hero-section img:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(28, 187, 180, 0.3);
}

/* Image placeholder styling - NCare Style */
.img-placeholder {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 4rem;
    min-height: 400px;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.img-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced card styling */
.card {
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.card-body {
    padding: 2rem;
}

/* Button enhancements - NCare Style */
.btn {
    border-radius: 50px;
    font-weight: 600;
    padding: 1rem 2.5rem;
    transition: all 0.4s ease;
    border: 2px solid transparent;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    border: none;
    color: var(--white);
    box-shadow: 0 8px 25px rgba(28, 187, 180, 0.3);
}

.btn-primary:hover,
.btn-primary:focus {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(28, 187, 180, 0.4);
    color: var(--white);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #FF8A65 100%);
    border: none;
    color: var(--white);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(28, 187, 180, 0.3);
}

.btn-lg {
    padding: 1.25rem 3rem;
    font-size: 1.1rem;
}

/* Typography enhancements - NCare Style */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.3;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.display-4 {
    font-weight: 800;
    font-size: 3.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.display-5 {
    font-weight: 700;
    color: var(--white);
}

/* Section Styling */
section {
    position: relative;
    padding: 5rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    position: relative;
}

.section-title h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

.section-title p {
    color: var(--text-light);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Background Sections */
.bg-light-custom {
    background: var(--section-bg);
}

.py-5 {
    padding: 5rem 0 !important;
}

/* Section spacing */
section {
    position: relative;
}

.hero-banner {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
}

/* Loading animations */
.loaded {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* High contrast improvements */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3) !important;
}

/* Resource page specific styles */
.resource-item {
    transition: all 0.3s ease;
}

.resource-item.d-none {
    display: none !important;
}

.accessibility-features {
    border-left: 3px solid var(--success-color);
    padding-left: 0.75rem;
    margin-left: 0.5rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* Alert enhancements */
.alert {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.alert-success {
    background: linear-gradient(135deg, #198754, #157347);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #0dcaf0, #0aa2c0);
    color: white;
}

/* Character counter */
.character-counter {
    text-align: right;
    margin-top: 0.25rem;
}

/* News Section */
.news-section {
    background: var(--section-bg);
}

.news-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.4s ease;
    box-shadow: 0 5px 20px rgba(28, 187, 180, 0.1);
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(28, 187, 180, 0.2);
}

.news-card .card-body {
    padding: 1.5rem;
}

.news-card .badge {
    background: var(--primary-color);
    color: var(--white);
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* Statistics Section */
.stats-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
}

.stat-item {
    text-align: center;
    padding: 2rem 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--white);
    display: block;
}

.stat-label {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-top: 0.5rem;
}

/* Badge Styling */
.badge {
    font-weight: 500;
    letter-spacing: 0.5px;
}

.bg-light {
    background: rgba(28, 187, 180, 0.1) !important;
    color: var(--primary-color) !important;
}

/* Responsive Design - NCare Style */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 80px 0 60px;
    }

    .hero-section img {
        margin-top: 2rem;
    }

    .hero-banner {
        padding: 2rem 0;
    }

    .contact-form {
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .contact-method {
        text-align: center;
        margin-bottom: 2rem;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .display-5 {
        font-size: 2rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 50px !important;
        margin-bottom: 0.5rem;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    section {
        padding: 3rem 0;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 60px 0 40px;
    }

    .hero-banner {
        padding: 1.5rem 0;
    }

    .display-4 {
        font-size: 2rem;
    }

    .display-5 {
        font-size: 1.75rem;
    }

    .lead {
        font-size: 1rem;
    }

    .btn {
        padding: 0.875rem 2rem;
        font-size: 0.9rem;
    }

    .btn-lg {
        padding: 1rem 2.5rem;
    }

    .section-title h2 {
        font-size: 1.75rem;
    }

    .img-placeholder {
        min-height: 250px;
        font-size: 3rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scroll Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}
