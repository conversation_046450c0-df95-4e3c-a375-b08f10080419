<?php
session_start();
require_once 'includes/config.php';

// Function to sanitize input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to validate email
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Initialize variables
$errors = [];
$success = false;

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Sanitize and validate inputs
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $subject = sanitize_input($_POST['subject'] ?? '');
    $message = sanitize_input($_POST['message'] ?? '');
    $accessibility_needs = isset($_POST['accessibility_needs']) ? 1 : 0;
    
    // Validation
    if (empty($name)) {
        $errors['name'] = 'Full name is required.';
    }
    
    if (empty($email)) {
        $errors['email'] = 'Email address is required.';
    } elseif (!validate_email($email)) {
        $errors['email'] = 'Please enter a valid email address.';
    }
    
    if (empty($subject)) {
        $errors['subject'] = 'Please select a subject.';
    }
    
    if (empty($message)) {
        $errors['message'] = 'Message is required.';
    } elseif (strlen($message) > 1000) {
        $errors['message'] = 'Message must be 1000 characters or less.';
    }
    
    // If no errors, process the form
    if (empty($errors)) {
        try {
            // Insert into database
            $stmt = $conn->prepare("INSERT INTO contact_submissions (name, email, phone, subject, message, accessibility_needs, submitted_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $stmt->bind_param("sssssi", $name, $email, $phone, $subject, $message, $accessibility_needs);
            
            if ($stmt->execute()) {
                $success = true;
                
                // Send email notification (optional - configure your email settings)
                $to = "<EMAIL>";
                $email_subject = "New Contact Form Submission: " . $subject;
                $email_body = "Name: " . $name . "\n";
                $email_body .= "Email: " . $email . "\n";
                $email_body .= "Phone: " . $phone . "\n";
                $email_body .= "Subject: " . $subject . "\n";
                $email_body .= "Accessibility Needs: " . ($accessibility_needs ? 'Yes' : 'No') . "\n\n";
                $email_body .= "Message:\n" . $message;
                
                $headers = "From: " . $email . "\r\n";
                $headers .= "Reply-To: " . $email . "\r\n";
                $headers .= "X-Mailer: PHP/" . phpversion();
                
                // Uncomment the line below to enable email sending
                // mail($to, $email_subject, $email_body, $headers);
                
            } else {
                $errors['general'] = 'There was an error submitting your message. Please try again.';
            }
            
            $stmt->close();
            
        } catch (Exception $e) {
            $errors['general'] = 'There was an error processing your request. Please try again later.';
        }
    }
}

// Return JSON response for AJAX requests
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'errors' => $errors,
        'message' => $success ? 'Thank you for your message! We will get back to you within 24 hours.' : 'Please correct the errors below.'
    ]);
    exit;
}

// For regular form submissions, redirect back with status
if ($success) {
    header("Location: index.php?contact=success#contact");
} else {
    $_SESSION['contact_errors'] = $errors;
    $_SESSION['contact_data'] = $_POST;
    header("Location: index.php?contact=error#contact");
}
exit;
?>
