<?php
require_once 'config.php';

// Function to create database tables
function createTables($conn) {
    $tables = [];

    // Enhanced events table
    $tables['events'] = "
        CREATE TABLE IF NOT EXISTS events (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            date DATE NOT NULL,
            time TIME,
            location VARCHAR(255) NOT NULL,
            accessibility_info TEXT,
            max_participants INT DEFAULT NULL,
            registration_required BOOLEAN DEFAULT FALSE,
            contact_email VARCHAR(255),
            contact_phone VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            status ENUM('active', 'cancelled', 'completed') DEFAULT 'active'
        )
    ";

    // Contact submissions table
    $tables['contact_submissions'] = "
        CREATE TABLE IF NOT EXISTS contact_submissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name <PERSON><PERSON>HA<PERSON>(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            subject VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            accessibility_needs BOOLEAN DEFAULT FALSE,
            submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('new', 'in_progress', 'resolved') DEFAULT 'new',
            response_sent BOOLEAN DEFAULT FALSE,
            notes TEXT
        )
    ";

    // Enhanced resources table
    $tables['resources'] = "
        CREATE TABLE IF NOT EXISTS resources (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            category ENUM('accessibility', 'legal', 'financial', 'health', 'education', 'employment', 'technology', 'other') NOT NULL,
            resource_type ENUM('document', 'link', 'video', 'audio', 'tool') NOT NULL,
            file_path VARCHAR(500),
            external_url VARCHAR(500),
            accessibility_features TEXT,
            language VARCHAR(10) DEFAULT 'en',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE,
            download_count INT DEFAULT 0
        )
    ";

    // News/announcements table
    $tables['news'] = "
        CREATE TABLE IF NOT EXISTS news (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            summary VARCHAR(500),
            author VARCHAR(255),
            published_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_published BOOLEAN DEFAULT TRUE,
            featured BOOLEAN DEFAULT FALSE
        )
    ";

    return $tables;
}

// Function to insert sample data
function insertSampleData($conn) {
    // Sample events
    $events = [
        [
            'title' => 'Wheelchair Basketball Tournament',
            'description' => 'Join us for an exciting wheelchair basketball tournament. All skill levels welcome!',
            'date' => '2025-08-15',
            'time' => '14:00:00',
            'location' => 'Community Sports Center, 456 Sports Ave',
            'accessibility_info' => 'Fully wheelchair accessible venue with accessible parking and restrooms',
            'max_participants' => 20,
            'registration_required' => true,
            'contact_email' => '<EMAIL>',
            'contact_phone' => '(*************'
        ],
        [
            'title' => 'Assistive Technology Workshop',
            'description' => 'Learn about the latest assistive technologies and how they can improve daily life.',
            'date' => '2025-08-22',
            'time' => '10:00:00',
            'location' => 'Support Org Main Building, Room 101',
            'accessibility_info' => 'ASL interpreter available, large print materials provided',
            'max_participants' => 15,
            'registration_required' => true,
            'contact_email' => '<EMAIL>',
            'contact_phone' => '(*************'
        ],
        [
            'title' => 'Monthly Support Group Meeting',
            'description' => 'A safe space to share experiences, challenges, and successes with peers.',
            'date' => '2025-08-30',
            'time' => '18:00:00',
            'location' => 'Support Org Main Building, Conference Room',
            'accessibility_info' => 'Wheelchair accessible, quiet room available for sensory breaks',
            'registration_required' => false,
            'contact_email' => '<EMAIL>',
            'contact_phone' => '(*************'
        ]
    ];

    foreach ($events as $event) {
        $stmt = $conn->prepare("INSERT INTO events (title, description, date, time, location, accessibility_info, max_participants, registration_required, contact_email, contact_phone) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssssibss",
            $event['title'],
            $event['description'],
            $event['date'],
            $event['time'],
            $event['location'],
            $event['accessibility_info'],
            $event['max_participants'],
            $event['registration_required'],
            $event['contact_email'],
            $event['contact_phone']
        );
        $stmt->execute();
    }

    // Sample resources
    $resources = [
        [
            'title' => 'Disability Rights Guide',
            'description' => 'Comprehensive guide to understanding your rights under the ADA and other disability laws.',
            'category' => 'legal',
            'resource_type' => 'document',
            'accessibility_features' => 'Screen reader compatible, available in large print',
            'language' => 'en'
        ],
        [
            'title' => 'Assistive Technology Database',
            'description' => 'Searchable database of assistive technologies with reviews and pricing information.',
            'category' => 'technology',
            'resource_type' => 'tool',
            'external_url' => 'https://example.com/assistive-tech-db',
            'accessibility_features' => 'Keyboard navigation, high contrast mode available',
            'language' => 'en'
        ]
    ];

    foreach ($resources as $resource) {
        $stmt = $conn->prepare("INSERT INTO resources (title, description, category, resource_type, external_url, accessibility_features, language) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("sssssss",
            $resource['title'],
            $resource['description'],
            $resource['category'],
            $resource['resource_type'],
            $resource['external_url'] ?? null,
            $resource['accessibility_features'],
            $resource['language']
        );
        $stmt->execute();
    }
}

// Main setup function
function setupDatabase() {
    global $conn;

    try {
        // Create tables
        $tables = createTables($conn);

        foreach ($tables as $tableName => $sql) {
            if ($conn->query($sql) === TRUE) {
                echo "Table '$tableName' created successfully.<br>";
            } else {
                echo "Error creating table '$tableName': " . $conn->error . "<br>";
            }
        }

        // Insert sample data
        insertSampleData($conn);
        echo "Sample data inserted successfully.<br>";

        echo "<br><strong>Database setup completed successfully!</strong><br>";

    } catch (Exception $e) {
        echo "Error during setup: " . $e->getMessage() . "<br>";
    }
}

// Run setup if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) == 'setup.php') {
    echo "<h2>Database Setup for Disability Support Organization</h2>";
    setupDatabase();
    echo "<br><a href='../index.php'>Go to Website</a>";
}
?>
