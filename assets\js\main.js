// Main JavaScript for Disability Support Organization Website

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initializeAccessibility();
    initializeContactForm();
    initializeNavigation();
    initializeAnnouncements();
    initializeImageLoading();
});

// Accessibility enhancements
function initializeAccessibility() {
    // Add keyboard navigation for cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.setAttribute('tabindex', '0');
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const link = card.querySelector('a');
                if (link) {
                    link.click();
                }
            }
        });
    });

    // Smooth scrolling for anchor links with focus management
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Set focus to target for screen readers
                setTimeout(() => {
                    targetElement.setAttribute('tabindex', '-1');
                    targetElement.focus();
                }, 500);
            }
        });
    });

    // Announce page changes to screen readers
    announceToScreenReader('Page loaded successfully. Use navigation menu to explore our services and resources.');
}

// Contact form functionality
function initializeContactForm() {
    const form = document.querySelector('.contact-form');
    if (!form) return;

    // Real-time validation
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateField(this);
            }
        });
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (validateForm()) {
            submitForm();
        } else {
            announceToScreenReader('Please correct the errors in the form before submitting.');
            // Focus on first error
            const firstError = form.querySelector('.is-invalid');
            if (firstError) {
                firstError.focus();
            }
        }
    });

    // Character counter for message field
    const messageField = document.getElementById('contact-message');
    if (messageField) {
        const maxLength = messageField.getAttribute('maxlength');
        const counter = document.createElement('div');
        counter.className = 'character-counter text-muted small';
        counter.setAttribute('aria-live', 'polite');
        messageField.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - messageField.value.length;
            counter.textContent = `${remaining} characters remaining`;

            if (remaining < 50) {
                counter.className = 'character-counter text-warning small';
            } else {
                counter.className = 'character-counter text-muted small';
            }
        }

        messageField.addEventListener('input', updateCounter);
        updateCounter();
    }
}

// Form validation functions
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    // Clear previous validation
    field.classList.remove('is-invalid');
    const feedback = field.parentNode.querySelector('.invalid-feedback');

    switch (field.type) {
        case 'email':
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                errorMessage = 'Email address is required.';
            } else if (value && !isValidEmail(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid email address.';
            }
            break;

        case 'text':
        case 'textarea':
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                errorMessage = `${field.labels[0].textContent.replace('*', '').trim()} is required.`;
            }
            break;

        case 'select-one':
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                errorMessage = 'Please select an option.';
            }
            break;
    }

    if (!isValid) {
        field.classList.add('is-invalid');
        if (feedback) {
            feedback.textContent = errorMessage;
        }
    }

    return isValid;
}

function validateForm() {
    const form = document.querySelector('.contact-form');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });

    return isValid;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Form submission
function submitForm() {
    const form = document.querySelector('.contact-form');
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;

    // Show loading state
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin" aria-hidden="true"></i> Sending...';
    submitButton.disabled = true;

    // Create FormData object
    const formData = new FormData(form);

    // Submit via AJAX
    fetch('contact_process.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            form.reset();
            announceToScreenReader('Your message has been sent successfully. We will get back to you within 24 hours.');
        } else {
            showAlert('danger', data.message);
            // Display field-specific errors
            Object.keys(data.errors).forEach(fieldName => {
                const field = form.querySelector(`[name="${fieldName}"]`);
                if (field) {
                    field.classList.add('is-invalid');
                    const feedback = field.parentNode.querySelector('.invalid-feedback');
                    if (feedback) {
                        feedback.textContent = data.errors[fieldName];
                    }
                }
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'There was an error sending your message. Please try again.');
    })
    .finally(() => {
        // Restore button state
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    });
}

// Navigation enhancements
function initializeNavigation() {
    const navToggler = document.querySelector('.navbar-toggler');
    const navCollapse = document.querySelector('.navbar-collapse');

    if (navToggler && navCollapse) {
        navToggler.addEventListener('click', function() {
            const isExpanded = this.getAttribute('aria-expanded') === 'true';
            announceToScreenReader(isExpanded ? 'Navigation menu closed' : 'Navigation menu opened');
        });
    }

    // Navbar stays white - no background change on scroll
    // Removed scroll-based color changes to maintain consistent white background
}

// Utility functions
function showAlert(type, message) {
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
    alertContainer.setAttribute('role', 'alert');
    alertContainer.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    const form = document.querySelector('.contact-form');
    form.insertBefore(alertContainer, form.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertContainer.parentNode) {
            alertContainer.remove();
        }
    }, 5000);
}

function announceToScreenReader(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'visually-hidden';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    setTimeout(() => {
        document.body.removeChild(announcement);
    }, 1000);
}

function initializeAnnouncements() {
    // Check for URL parameters to show messages
    const urlParams = new URLSearchParams(window.location.search);
    const contactStatus = urlParams.get('contact');

    if (contactStatus === 'success') {
        showAlert('success', 'Thank you for your message! We will get back to you within 24 hours.');
        announceToScreenReader('Your message has been sent successfully.');
    } else if (contactStatus === 'error') {
        showAlert('danger', 'There was an error with your submission. Please try again.');
    }
}

function initializeImageLoading() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.addEventListener('load', function() {
            this.classList.add('loaded');
        });
    });
}
