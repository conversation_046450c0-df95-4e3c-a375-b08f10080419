<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive resources for people with disabilities including legal guides, assistive technology, employment resources, and accessibility tools">
    <meta name="keywords" content="disability resources, assistive technology, legal rights, employment, accessibility tools">
    <title>Resources - NCare Support NDIS Services</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Skip Navigation Link for Screen Readers -->
    <a href="#main-content" class="skip-link visually-hidden-focusable">Skip to main content</a>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top" role="navigation" aria-label="Main navigation">
        <div class="container">
            <a class="navbar-brand" href="index.php" aria-label="NCare Support - Go to homepage">
                <i class="fas fa-heart" aria-hidden="true"></i> NCare Support
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation menu">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto" role="menubar">
                    <li class="nav-item" role="none">
                        <a class="nav-link" href="index.php#home" role="menuitem">Home</a>
                    </li>
                    <li class="nav-item" role="none">
                        <a class="nav-link" href="index.php#services" role="menuitem">Services</a>
                    </li>
                    <li class="nav-item" role="none">
                        <a class="nav-link" href="index.php#events" role="menuitem">Events</a>
                    </li>
                    <li class="nav-item" role="none">
                        <a class="nav-link active" href="resources.php" role="menuitem" aria-current="page">Resources</a>
                    </li>
                    <li class="nav-item" role="none">
                        <a class="nav-link" href="index.php#contact" role="menuitem">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Page Header -->
        <section class="hero-banner py-5 bg-primary text-light text-center" aria-labelledby="resources-heading">
            <div class="container">
                <h1 id="resources-heading" class="display-4 fw-bold mb-3">
                    <i class="fas fa-book" aria-hidden="true"></i> Resources & Support
                </h1>
                <p class="lead">Access valuable resources, tools, and information to support your journey</p>
            </div>
        </section>

        <!-- Resource Categories -->
        <section class="py-5" aria-labelledby="categories-heading">
            <div class="container">
                <h2 id="categories-heading" class="text-center mb-5">Resource Categories</h2>
                
                <!-- Filter Buttons -->
                <div class="text-center mb-4">
                    <div class="btn-group" role="group" aria-label="Filter resources by category">
                        <button type="button" class="btn btn-outline-primary active" data-filter="all">All Resources</button>
                        <button type="button" class="btn btn-outline-primary" data-filter="legal">Legal Rights</button>
                        <button type="button" class="btn btn-outline-primary" data-filter="technology">Technology</button>
                        <button type="button" class="btn btn-outline-primary" data-filter="employment">Employment</button>
                        <button type="button" class="btn btn-outline-primary" data-filter="health">Health</button>
                        <button type="button" class="btn btn-outline-primary" data-filter="education">Education</button>
                    </div>
                </div>

                <!-- Resources Grid -->
                <div class="row g-4" id="resources-grid">
                    <?php
                    include 'includes/config.php';
                    $result = $conn->query("SELECT * FROM resources WHERE is_active = 1 ORDER BY category, title");
                    
                    if ($result && $result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            $iconClass = '';
                            switch($row['resource_type']) {
                                case 'document': $iconClass = 'fas fa-file-pdf'; break;
                                case 'link': $iconClass = 'fas fa-external-link-alt'; break;
                                case 'video': $iconClass = 'fas fa-video'; break;
                                case 'audio': $iconClass = 'fas fa-volume-up'; break;
                                case 'tool': $iconClass = 'fas fa-tools'; break;
                                default: $iconClass = 'fas fa-file';
                            }
                            
                            echo '<div class="col-md-6 col-lg-4 resource-item" data-category="' . htmlspecialchars($row['category']) . '">';
                            echo '<article class="card h-100" role="article">';
                            echo '<div class="card-body">';
                            echo '<h3 class="card-title">';
                            echo '<i class="' . $iconClass . ' text-primary me-2" aria-hidden="true"></i>';
                            echo htmlspecialchars($row['title']);
                            echo '</h3>';
                            echo '<p class="card-text">' . htmlspecialchars($row['description']) . '</p>';
                            
                            if (!empty($row['accessibility_features'])) {
                                echo '<div class="accessibility-features mb-2">';
                                echo '<small class="text-muted">';
                                echo '<i class="fas fa-universal-access text-success me-1" aria-hidden="true"></i>';
                                echo 'Accessibility: ' . htmlspecialchars($row['accessibility_features']);
                                echo '</small>';
                                echo '</div>';
                            }
                            
                            echo '<div class="card-footer bg-transparent">';
                            echo '<span class="badge bg-secondary me-2">' . ucfirst($row['category']) . '</span>';
                            echo '<span class="badge bg-info">' . ucfirst($row['resource_type']) . '</span>';
                            
                            if (!empty($row['external_url'])) {
                                echo '<div class="mt-2">';
                                echo '<a href="' . htmlspecialchars($row['external_url']) . '" class="btn btn-primary btn-sm" target="_blank" rel="noopener noreferrer">';
                                echo '<i class="fas fa-external-link-alt me-1" aria-hidden="true"></i>Access Resource';
                                echo '<span class="visually-hidden"> (opens in new window)</span>';
                                echo '</a>';
                                echo '</div>';
                            } elseif (!empty($row['file_path'])) {
                                echo '<div class="mt-2">';
                                echo '<a href="' . htmlspecialchars($row['file_path']) . '" class="btn btn-primary btn-sm" download>';
                                echo '<i class="fas fa-download me-1" aria-hidden="true"></i>Download';
                                echo '</a>';
                                echo '</div>';
                            }
                            
                            echo '</div>';
                            echo '</div>';
                            echo '</article>';
                            echo '</div>';
                        }
                    } else {
                        echo '<div class="col-12">';
                        echo '<div class="alert alert-info text-center">';
                        echo '<i class="fas fa-info-circle me-2"></i>';
                        echo 'Resources are being updated. Please check back soon or contact us for immediate assistance.';
                        echo '</div>';
                        echo '</div>';
                    }
                    ?>
                </div>
            </div>
        </section>

        <!-- Quick Access Tools -->
        <section class="py-5 bg-light" aria-labelledby="tools-heading">
            <div class="container">
                <h2 id="tools-heading" class="text-center mb-5">Quick Access Tools</h2>
                <div class="row g-4">
                    <div class="col-md-6 col-lg-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-phone fa-3x text-primary mb-3" aria-hidden="true"></i>
                                <h3 class="card-title">Crisis Hotline</h3>
                                <p class="card-text">24/7 support for urgent situations</p>
                                <a href="tel:988" class="btn btn-danger">Call 988</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-tty fa-3x text-primary mb-3" aria-hidden="true"></i>
                                <h3 class="card-title">TTY Services</h3>
                                <p class="card-text">Text telephone services for hearing impaired</p>
                                <a href="tel:711" class="btn btn-primary">Dial 711</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-language fa-3x text-primary mb-3" aria-hidden="true"></i>
                                <h3 class="card-title">Translation</h3>
                                <p class="card-text">Language interpretation services</p>
                                <button class="btn btn-primary" onclick="requestTranslation()">Request Service</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-question-circle fa-3x text-primary mb-3" aria-hidden="true"></i>
                                <h3 class="card-title">Ask for Help</h3>
                                <p class="card-text">Get personalized assistance</p>
                                <a href="index.php#contact" class="btn btn-primary">Contact Us</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Emergency Resources -->
        <section class="py-5" aria-labelledby="emergency-heading">
            <div class="container">
                <h2 id="emergency-heading" class="text-center mb-5 text-danger">
                    <i class="fas fa-exclamation-triangle" aria-hidden="true"></i> Emergency Resources
                </h2>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="alert alert-danger" role="alert">
                            <h3 class="alert-heading">In Case of Emergency</h3>
                            <p>If you are experiencing a life-threatening emergency, call <strong>911</strong> immediately.</p>
                            <hr>
                            <p class="mb-0">For mental health crises, call the <strong>988 Suicide & Crisis Lifeline</strong> at <a href="tel:988" class="alert-link">988</a></p>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3>Important Emergency Contacts</h3>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2"><strong>Emergency Services:</strong> <a href="tel:911">911</a></li>
                                    <li class="mb-2"><strong>Crisis Lifeline:</strong> <a href="tel:988">988</a></li>
                                    <li class="mb-2"><strong>TTY Emergency:</strong> <a href="tel:711">711</a></li>
                                    <li class="mb-2"><strong>Poison Control:</strong> <a href="tel:18002221222">1-************</a></li>
                                    <li class="mb-2"><strong>Disability Rights Hotline:</strong> <a href="tel:18004663242">**************</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>Contact Us</h5>
                    <p>
                        <i class="fas fa-envelope" aria-hidden="true"></i> Email: <EMAIL><br>
                        <i class="fas fa-phone" aria-hidden="true"></i> Phone: (*************<br>
                        <i class="fas fa-tty" aria-hidden="true"></i> TTY: (*************
                    </p>
                </div>
                <div class="col-md-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php#home" class="text-light">Home</a></li>
                        <li><a href="index.php#services" class="text-light">Services</a></li>
                        <li><a href="index.php#events" class="text-light">Events</a></li>
                        <li><a href="resources.php" class="text-light">Resources</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Follow Us</h5>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <p>&copy; 2025 Support Organization. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/resources.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
